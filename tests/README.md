# Тесты для Editor Service

Этот проект содержит интеграционные тесты для сервиса редактирования страниц.

## Структура тестов

```
tests/
├── config/
│   ├── jest.setup.cjs          # Настройка тестовой среды
│   └── jest.teardown.cjs       # Очистка после тестов
├── integration/
│   ├── __data__/               # Тестовые данные
│   ├── page.service.test.ts    # Тесты для PageService
│   ├── page.resolver.test.ts   # Тесты для GraphQL резолвера
│   ├── page.validation.test.ts # Тесты валидации
│   ├── database.test.ts        # Тесты базы данных
│   ├── test-setup.ts           # Настройка тестового приложения
│   └── test-user.ts            # Тестовые пользователи
├── jest.config.cjs             # Конфигурация Jest для интеграционных тестов
├── jest-unit.config.cjs        # Конфигурация Jest для unit тестов
└── user-session.ts             # Утилиты для GraphQL тестов
```

## Запуск тестов

### Все тесты
```bash
pnpm test
```

### Только интеграционные тесты
```bash
pnpm test:integration
```

### Только unit тесты
```bash
pnpm test:unit
```

### Конкретный тест
```bash
pnpm test -- page.service.test.ts
```

## Тестовая среда

Тесты используют:
- **PostgreSQL** в Docker контейнере (testcontainers)
- **Redis** в Docker контейнере (testcontainers)
- **Drizzle ORM** для работы с базой данных
- **GraphQL** для тестирования API

### Настройка базы данных

При запуске тестов автоматически:
1. Создается PostgreSQL контейнер
2. Применяются миграции Drizzle
3. Создается функция `gen_uuid_v7()`
4. Создаются все необходимые таблицы

## Тестируемые компоненты

### PageService
- ✅ Создание страниц
- ✅ Получение страниц по ID
- ✅ Удаление страниц
- ✅ Валидация родительских страниц
- ✅ Каскадное удаление

### PageResolver (GraphQL)
- ✅ Мутация `createPage`
- ✅ Запрос `page`
- ✅ Мутация `deletePage`
- ✅ Валидация входных данных
- ✅ Обработка ошибок

### Валидация
- ✅ UUID валидация
- ✅ Boolean валидация
- ✅ Опциональные поля
- ✅ Специальные символы
- ✅ Длинные строки
- ✅ Сложные JSON объекты

## Тестовые данные

Тестовые данные находятся в `__data__/page-test-data.json` и включают:
- Базовую страницу
- Сложную страницу с разметкой
- Заблокированную страницу
- Пустую страницу

## Отладка тестов

### Логи
Тесты используют уровень логирования `WARN` для уменьшения шума.

### База данных
Каждый тест использует свою изолированную базу данных в контейнере.

### GraphQL запросы
Все GraphQL запросы логируются в консоль при ошибках.

## Добавление новых тестов

1. Создайте новый файл в `tests/integration/`
2. Импортируйте необходимые компоненты из `test-setup.ts`
3. Используйте `userSession` для GraphQL запросов
4. Используйте `pageService` для прямого тестирования сервиса
5. Добавьте тестовые данные в `__data__/` если необходимо

### Пример теста

```typescript
import { describe, expect, it } from '@jest/globals';
import { app, pageService, userSession } from './test-setup';

describe('My Test', () => {
    it('should do something', async () => {
        const page = await pageService.create({ title: 'Test' });
        expect(page.title).toBe('Test');
    });
});
```
