import { DecodeJwtAuth } from '@skillspace/access';

export const SCHOOL_ID = '6fc112f6-e471-4d83-94c2-ff447f43d5e3';
export const SCHOOL_NAME = 'Школа 1';

export const PLANS_USER: DecodeJwtAuth = {
    userId: '6fc112f6-e471-4d83-94c2-ff447f43d5e1',
    role: 'ROLE_STUDENT' as const,
    actions: [],
    schoolId: SCHOOL_ID,
    userName: 'Иван Васильевич',
    userEmail: '<EMAIL>',
    unionAuthKey: '23',
};
