import { JSONContent } from '@tiptap/core';
import * as Y from 'yjs';

import { CreatePageDto } from '../../src/modules/editor/application/services/page.dto';
import { pageService, userSession } from './test-setup';
import { RealHocuspocusTestClient } from './utils/real-hocuspocus-client';

describe('Simple Binary Updates Tests', () => {
    const baseUrl = 'http://localhost:3000'; // Используем тестовый сервер из test-setup

    describe('Y.js Binary Operations', () => {
        it('должен корректно работать с Y.js документом', async () => {
            // 1. Создаем Y.Doc локально
            const localDoc = new Y.Doc();
            const fragment = localDoc.getXmlFragment('default');
            
            // 2. Добавляем контент в Y.Doc
            const xmlText = new Y.XmlText();
            xmlText.insert(0, 'Тестовый контент');
            fragment.insert(0, [xmlText]);

            // 3. Получаем бинарное состояние
            const binaryState = Y.encodeStateAsUpdate(localDoc);
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // 4. Создаем новый документ и применяем обновление
            const newDoc = new Y.Doc();
            Y.applyUpdate(newDoc, binaryState);

            // 5. Проверяем, что контент сохранился
            const newFragment = newDoc.getXmlFragment('default');
            expect(newFragment.length).toBe(1);
            
            const firstElement = newFragment.get(0);
            expect(firstElement).toBeDefined();
        });

        it('должен создать страницу и получить её через GraphQL', async () => {
            // 1. Создаем документ через pageService
            const pageDto: CreatePageDto = {
                serviceId: 'simple-test-service',
                entityId: 'simple-test-entity',
                schoolId: 'simple-test-school',
            };

            const page = await pageService.create(pageDto);
            expect(page).toBeDefined();
            expect(page.id).toBeDefined();

            // 2. Получаем документ через GraphQL
            const retrievedPage = await userSession.getPage(page.id);
            expect(retrievedPage).toBeDefined();
            expect(retrievedPage.id).toBe(page.id);
            expect(retrievedPage.serviceId).toBe('simple-test-service');
            expect(retrievedPage.entityId).toBe('simple-test-entity');
            expect(retrievedPage.schoolId).toBe('simple-test-school');
        });

        it('должен создать RealHocuspocusTestClient и проверить его методы', async () => {
            // 1. Создаем документ
            const pageDto: CreatePageDto = {
                serviceId: 'client-test-service',
                entityId: 'client-test-entity',
                schoolId: 'client-test-school',
            };

            const page = await pageService.create(pageDto);

            // 2. Создаем клиент
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            // 3. Проверяем начальное состояние
            expect(client.isConnected()).toBe(false);
            expect(client.isSynced()).toBe(false);

            // 4. Проверяем Y.Doc
            const ydoc = client.getYDoc();
            expect(ydoc).toBeInstanceOf(Y.Doc);

            // 5. Проверяем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);

            // 6. Добавляем контент
            client.insertText('Тестовый текст');
            
            // 7. Проверяем, что контент добавился
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // 8. Проверяем, что бинарное состояние изменилось
            const newBinaryState = client.getBinaryState();
            expect(newBinaryState.length).toBeGreaterThan(binaryState.length);
        });

        it('должен работать с различными типами контента', async () => {
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: 'test-doc-id',
            });

            // Тестируем различные методы
            client.insertHeading('Заголовок', 1);
            client.insertText('Параграф');
            client.insertBoldText('Жирный текст');
            client.insertItalicText('Курсивный текст');
            client.insertBulletList(['Элемент 1', 'Элемент 2']);
            client.insertOrderedList(['Пункт 1', 'Пункт 2']);
            client.insertTaskList([
                { text: 'Задача 1', checked: true },
                { text: 'Задача 2', checked: false }
            ]);
            client.insertTable(2, 2);
            client.insertCodeBlock('console.log("test");', 'javascript');
            client.insertBlockquote('Цитата');
            client.insertImage('https://example.com/image.jpg', 'Тестовое изображение');
            client.insertHorizontalRule();

            // Проверяем, что все добавилось
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // Очищаем содержимое
            client.clearContent();
            const clearedContent = client.getContent();
            expect(clearedContent.content).toEqual([]);
        });

        it('должен сериализовать Y.js обновления в JSON', async () => {
            // 1. Создаем Y.Doc с контентом
            const doc = new Y.Doc();
            const fragment = doc.getXmlFragment('default');
            
            // 2. Добавляем различные типы контента
            const text1 = new Y.XmlText();
            text1.insert(0, 'Первый текст');
            fragment.insert(0, [text1]);
            
            const text2 = new Y.XmlText();
            text2.insert(0, 'Второй текст');
            fragment.insert(1, [text2]);

            // 3. Получаем бинарное состояние
            const binaryState = Y.encodeStateAsUpdate(doc);
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // 4. Создаем новый документ и применяем обновление
            const newDoc = new Y.Doc();
            Y.applyUpdate(newDoc, binaryState);

            // 5. Проверяем, что контент восстановился
            const newFragment = newDoc.getXmlFragment('default');
            expect(newFragment.length).toBe(2);

            // 6. Проверяем содержимое элементов
            const element1 = newFragment.get(0);
            const element2 = newFragment.get(1);
            expect(element1).toBeDefined();
            expect(element2).toBeDefined();

            if (element1 && 'toString' in element1) {
                expect(element1.toString()).toBe('Первый текст');
            }
            if (element2 && 'toString' in element2) {
                expect(element2.toString()).toBe('Второй текст');
            }
        });
    });
});
