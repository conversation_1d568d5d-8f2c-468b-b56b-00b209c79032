import 'reflect-metadata';

import { setTimeout as delay } from 'node:timers/promises';
import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { createLogger } from '@skillspace/logger';

import { AppModule } from '../../src/app.module.js';
import { setupGlobalMiddlewares } from '../../src/bootstrap-setup.js';
import { DRIZZLE_DB } from '../../src/drizzle/drizzle.module.js';
import { DrizzleDB } from '../../src/drizzle/drizzle.types.js';
import { PageService } from '../../src/modules/editor/application/services/page.service.js';
import { PageResolver } from '../../src/modules/editor/presentation/graphql/page.resolver.js';
import { createGqlTestSession, GqlTestSession } from '../user-session.js';
import { PLANS_USER } from './test-user.js';

let app: INestApplication;
let testDb: DrizzleDB;

let pageService: PageService;
let pageResolver: PageResolver;

let userSession: GqlTestSession;

beforeAll(async () => {
    const builder = Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleFixture = await builder.compile();
    app = moduleFixture.createNestApplication({ logger: await createLogger() });

    setupGlobalMiddlewares(app);

    await app.init();

    pageService = app.get<PageService>(PageService);
    pageResolver = app.get<PageResolver>(PageResolver);

    userSession = await createGqlTestSession(app, PLANS_USER);
    testDb = app.get<DrizzleDB>(DRIZZLE_DB);
}, 30000);

beforeEach(() => {
    jest.clearAllMocks();
});

afterAll(async () => {
    if (app) {
        const controller = new AbortController();
        const { signal } = controller;

        await delay(100);

        await Promise.race([
            app.close(),
            delay(5000, undefined, { signal }).then(() => {
                throw new Error('App close timeout');
            }),
        ]);
    }
}, 10000);

export { app, pageService, pageResolver, testDb, userSession };
