import { randomUUID } from 'node:crypto';
import { NotFoundException } from '@nestjs/common';

import { CreatePageDto } from '../../src/modules/editor/application/services/page.dto';
import { app, pageService } from './test-setup';

describe('PageService Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('create', () => {
        it('должен создать страницу с минимальными данными', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'test-service',
                entityId: 'test-entity',
                schoolId: 'test-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.id).toBeDefined();
            expect(page.serviceId).toBe('test-service');
            expect(page.entityId).toBe('test-entity');
            expect(page.schoolId).toBe('test-school');
            expect(page.ydoc).toBeNull();
            expect(page.createdAt).toBeDefined();
            expect(page.updatedAt).toBeDefined();
        });
    });

    describe('findById', () => {
        it('должен найти существующую страницу', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'find-service',
                entityId: 'find-entity',
                schoolId: 'find-school',
            });

            // Ищем её
            const foundPage = await pageService.findById(createdPage.id);

            expect(foundPage).toBeDefined();
            expect(foundPage.id).toBe(createdPage.id);
            expect(foundPage.serviceId).toBe('find-service');
            expect(foundPage.entityId).toBe('find-entity');
            expect(foundPage.schoolId).toBe('find-school');
        });

        it('должен выбросить NotFoundException для несуществующей страницы', async () => {
            const nonExistentId = randomUUID();

            await expect(pageService.findById(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.findById(nonExistentId)).rejects.toThrow(
                `Page with ID ${nonExistentId} not found`,
            );
        });
    });

    describe('forceDelete', () => {
        it('должен удалить существующую страницу', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'delete-service',
                entityId: 'delete-entity',
                schoolId: 'delete-school',
            });

            // Проверяем, что она существует
            const foundPage = await pageService.findById(createdPage.id);
            expect(foundPage).toBeDefined();

            // Удаляем её
            await pageService.forceDelete(createdPage.id);

            // Проверяем, что она больше не существует
            await expect(pageService.findById(createdPage.id)).rejects.toThrow(NotFoundException);
        });

        it('должен выбросить NotFoundException при попытке удалить несуществующую страницу', async () => {
            const nonExistentId = randomUUID();

            await expect(pageService.forceDelete(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.forceDelete(nonExistentId)).rejects.toThrow(
                `Page with ID ${nonExistentId} not found`,
            );
        });
    });

    describe('update', () => {
        it('должен обновить содержимое страницы', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'update-service',
                entityId: 'update-entity',
                schoolId: 'update-school',
            });

            // Обновляем её
            const updateData = {
                content: {
                    type: 'doc',
                    content: [
                        {
                            type: 'paragraph',
                            content: [
                                {
                                    type: 'text',
                                    text: 'Updated content',
                                },
                            ],
                        },
                    ],
                },
                textContent: 'Updated content',
            };

            await pageService.update(createdPage.id, updateData);

            // Проверяем, что обновление прошло успешно
            const updatedPage = await pageService.findById(createdPage.id);
            expect(updatedPage.content).toEqual(updateData.content);
            expect(updatedPage.textContent).toBe(updateData.textContent);
        });

        it('должен обновить только указанные поля', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'partial-update-service',
                entityId: 'partial-update-entity',
                schoolId: 'partial-update-school',
            });

            // Обновляем только textContent
            const updateData = {
                textContent: 'Only text content updated',
            };

            await pageService.update(createdPage.id, updateData);

            // Проверяем, что обновление прошло успешно
            const updatedPage = await pageService.findById(createdPage.id);
            expect(updatedPage.textContent).toBe(updateData.textContent);
            expect(updatedPage.content).toBeNull(); // Должно остаться null
        });
    });

    describe('edge cases', () => {
        it('должен создать страницу с минимальными данными', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'edge-service',
                entityId: 'edge-entity',
                schoolId: 'edge-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('edge-service');
            expect(page.entityId).toBe('edge-entity');
            expect(page.schoolId).toBe('edge-school');
        });

        it('должен создать страницу с null content по умолчанию', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'null-service',
                entityId: 'null-entity',
                schoolId: 'null-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.content).toBeNull();
            expect(page.ydoc).toBeNull();
        });

        it('должен создать страницу с уникальными entityId', async () => {
            const createPageDto1: CreatePageDto = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-1',
                schoolId: 'unique-school',
            };

            const createPageDto2: CreatePageDto = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-2',
                schoolId: 'unique-school',
            };

            const page1 = await pageService.create(createPageDto1);
            const page2 = await pageService.create(createPageDto2);

            expect(page1).toBeDefined();
            expect(page2).toBeDefined();
            expect(page1.id).not.toBe(page2.id);
            expect(page1.entityId).toBe('unique-entity-1');
            expect(page2.entityId).toBe('unique-entity-2');
        });
    });
});
