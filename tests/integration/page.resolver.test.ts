import { CreatePageDto } from '../../src/modules/editor/application/services/page.dto';
import { app, pageService, userSession } from './test-setup';

// Вспомогательная функция для создания базового DTO с обязательными полями
function createBaseDto(overrides: Partial<CreatePageDto> = {}): CreatePageDto {
    return {
        serviceId: 'test-service',
        entityId: 'test-entity',
        schoolId: 'test-school',
        ...overrides,
    };
}

describe('PageResolver Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(userSession).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('page query', () => {
        it('должен получить страницу по ID через GraphQL', async () => {
            const createdPage = await pageService.create(
                createBaseDto({
                    entityId: 'query-entity',
                }),
            );

            const page = await userSession.getPage(createdPage.id);

            expect(page).toBeDefined();
            expect(page.id).toBe(createdPage.id);
            expect(page.serviceId).toBe('test-service');
            expect(page.entityId).toBe('query-entity');
            expect(page.schoolId).toBe('test-school');
            expect(page.content).toBeNull();
            expect(page.textContent).toBeNull();
        });

        it('должен вернуть ошибку для несуществующей страницы', async () => {
            const nonExistentId = '01234567-89ab-cdef-0123-456789abcdef';

            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.getPage(nonExistentId);
            });

            expect(errorMessage).toContain('not found');
        });
    });

    describe('deletePage mutation', () => {
        it('должен удалить страницу через GraphQL', async () => {
            // Создаем страницу через pageService
            const dto = createBaseDto({
                entityId: 'delete-entity',
            });

            const createdPage = await pageService.create(dto);

            // Проверяем, что страница существует
            const page = await userSession.getPage(createdPage.id);
            expect(page).toBeDefined();

            // Удаляем страницу через GraphQL
            const deleteResult = await userSession.deletePage(createdPage.id);
            expect(deleteResult).toBe(true);

            // Проверяем, что страница больше не существует
            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.getPage(createdPage.id);
            });

            expect(errorMessage).toContain('not found');
        });

        it('должен вернуть ошибку при попытке удалить несуществующую страницу', async () => {
            const nonExistentId = '01234567-89ab-cdef-0123-456789abcdef';

            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.deletePage(nonExistentId);
            });

            expect(errorMessage).toContain('not found');
        });
    });

    describe('GraphQL integration with pageService', () => {
        it('должен получить страницу созданную через pageService', async () => {
            const dto: CreatePageDto = {
                serviceId: 'valid-service-123',
                entityId: 'valid-entity-456',
                schoolId: 'valid-school-789',
            };

            const createdPage = await pageService.create(dto);
            const page = await userSession.getPage(createdPage.id);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('valid-service-123');
            expect(page.entityId).toBe('valid-entity-456');
            expect(page.schoolId).toBe('valid-school-789');
        });

        it('должен обработать специальные символы в полях', async () => {
            const dto: CreatePageDto = {
                serviceId: 'service-with-special-chars-!@#$%',
                entityId: 'entity_with_underscores_and_numbers_123',
                schoolId: 'school-with-émojis-🏫',
            };

            const createdPage = await pageService.create(dto);
            const page = await userSession.getPage(createdPage.id);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('service-with-special-chars-!@#$%');
            expect(page.entityId).toBe('entity_with_underscores_and_numbers_123');
            expect(page.schoolId).toBe('school-with-émojis-🏫');
        });

        it('должен обработать длинные строки', async () => {
            const longServiceId = 'service-' + 'A'.repeat(100);
            const longEntityId = 'entity-' + 'B'.repeat(100);
            const longSchoolId = 'school-' + 'C'.repeat(100);

            const dto: CreatePageDto = {
                serviceId: longServiceId,
                entityId: longEntityId,
                schoolId: longSchoolId,
            };

            const createdPage = await pageService.create(dto);
            const page = await userSession.getPage(createdPage.id);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe(longServiceId);
            expect(page.entityId).toBe(longEntityId);
            expect(page.schoolId).toBe(longSchoolId);
        });
    });
});
