module.exports = {
    moduleFileExtensions: ['js', 'json', 'ts'],
    rootDir: '../',
    modulePathIgnorePatterns: ['<rootDir>/tests/'],
    modulePaths: ['.'],
    testRegex: '.*\\.spec\\.ts$',
    transform: {
        '^.+\\.(t|j)s?$': ['@swc/jest', { swcrc: true }],
    },
    testEnvironment: 'node',
    watchPathIgnorePatterns: ['!<rootDir>/src/', '!<rootDir>/tests/', '<rootDir>/.data/'],
    moduleNameMapper: {
        '^(\\.{1,2}/.*)\\.js$': '$1', // Игнорируем расширение .js для импортов
    },
    extensionsToTreatAsEsm: ['.ts'], // Обрабатывать .ts как ES-модули
    transformIgnorePatterns: [
        'node_modules/(?!(got)/)', // Разрешаем трансформацию для got
    ],
};
