module.exports = async () => {
    //                                   # bytes |  KB  | MB   | GB
    const gbNow = process.memoryUsage().heapUsed / 1024 / 1024 / 1024;
    const gbRounded = Math.round(gbNow * 100) / 100;
    console.log(`<PERSON>ap allocated ${gbRounded} GB`);

    // Очищаем все таймеры
    const timers = require('timers');
    if (timers.clearInterval) {
        // Очищаем все активные интервалы
        for (let i = 1; i < 10000; i++) {
            try {
                clearInterval(i);
                clearTimeout(i);
            } catch (e) {
                // Игнорируем ошибки
            }
        }
    }

    if (global.__DATABASE_CONTAINER__) {
        global.__DATABASE_CONTAINER__.stop();
    }

    if (global.__REDIS_CONTAINER__) {
        global.__REDIS_CONTAINER__.stop();
    }

    setTimeout(() => {
        process.exit(0);
    }, 500);
};
