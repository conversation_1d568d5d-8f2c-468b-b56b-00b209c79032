/* eslint-disable @typescript-eslint/no-require-imports */

// Отключаем все таймеры OpenTelemetry
const originalSetInterval = global.setInterval;
const originalSetTimeout = global.setTimeout;

global.setInterval = (fn, delay, ...args) => {
    // Блокируем OpenTelemetry таймеры
    if (
        fn.toString().includes('PeriodicExportingMetricReader') ||
        fn.toString().includes('metrics') ||
        fn.toString().includes('telemetry')
    ) {
        return { unref: () => {} };
    }
    return originalSetInterval(fn, delay, ...args);
};

global.setTimeout = (fn, delay, ...args) => {
    // Блокируем OpenTelemetry таймеры
    if (
        fn.toString().includes('PeriodicExportingMetricReader') ||
        fn.toString().includes('metrics') ||
        fn.toString().includes('telemetry')
    ) {
        return { unref: () => {} };
    }
    return originalSetTimeout(fn, delay, ...args);
};

afterEach(async () => {
    jest.clearAllTimers();
    // Принудительно завершаем все промисы
    await new Promise((resolve) => setImmediate(resolve));
});

// Глобальный cleanup
afterAll(async () => {
    // Ждем завершения всех асинхронных операций
    await new Promise((resolve) => setTimeout(resolve, 100));

    jest.clearAllMocks();
    jest.restoreAllMocks();
});
