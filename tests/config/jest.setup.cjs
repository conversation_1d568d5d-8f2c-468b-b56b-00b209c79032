/* eslint-disable @typescript-eslint/no-require-imports */
const { PostgreSqlContainer } = require('@testcontainers/postgresql');
const { RedisContainer } = require('@testcontainers/redis');
const { LogLevel } = require('@skillspace/logger');
const { execSync } = require('child_process');

module.exports = async () => {
    // Создаем и запускаем контейнер базы данных
    const dbContainer = await new PostgreSqlContainer('postgres:13.3-alpine').start();
    const baseUri = dbContainer.getConnectionUri(); //postgres://test:test@localhost:32773/test

    // Создаем и запускаем контейнер Redis
    const redisContainer = await new RedisContainer('redis:6.2-alpine').withReuse().start();
    // const redisHost = redisContainer.getHost();
    // const redisPort = redisContainer.getMappedPort(6379);

    // Устанавливаем переменные окружения
    process.env.DATABASE_URL = baseUri;
    // process.env.REDIS_HOST = redisHost;
    // process.env.REDIS_PORT = redisPort.toString();

    process.env.OTEL_DISABLED = 'true';
    process.env.LOG_LEVEL = LogLevel.Silent;

    // UUID generation is now handled in application logic using uuid library

    console.log('Applying Drizzle migrations...');
    execSync('npx drizzle-kit push', { stdio: 'inherit' });
    console.log('Drizzle migrations applied successfully.');

    // Сохраняем ссылки на контейнеры в глобальном объекте
    global.__DATABASE_CONTAINER__ = dbContainer;
    global.__REDIS_CONTAINER__ = redisContainer;

    console.log('PostgreSQL container started with URI:', process.env.DATABASE_URL);
};
