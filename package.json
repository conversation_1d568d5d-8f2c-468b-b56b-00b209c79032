{"name": "docs", "version": "0.0.1", "description": "Сервис редактирования", "author": "Денис Благовещенский", "license": "UNLICENSED", "private": true, "scripts": {"preinstall": "only-allow pnpm", "build": "nest build", "format": "prettier --config prettier.config.cjs --write \"src/**/*.ts\" \"tests/**/*.ts\"", "lint": "eslint \"{src,tests}/**/*.ts\"", "lint:fix": "eslint \"{src,tests}/**/*.ts\" --fix", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "db:generate": "drizzle-kit generate --config drizzle.config.ts", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "test": "jest --config='tests/jest.config.cjs' --runInBand -b", "test:unit": "jest --config='tests/jest-unit.config.cjs'", "test:integration": "jest --config='tests/jest.config.cjs' --testPathPattern='integration' --runInBand", "test:ws": "jest --config='tests/jest.config.cjs' --testPathPattern='websocket' --runInBand", "client": "cd client && node server.js", "client:debug": "cd client && bash debug.sh", "client:test": "cd client && bash test.sh", "client:debug:win": "cd client && powershell -ExecutionPolicy Bypass -File debug.ps1", "client:test:win": "cd client && powershell -ExecutionPolicy Bypass -File test.ps1", "react-client": "cd react-client && npm run dev", "react-client:build": "cd react-client && npm run build", "dev:full": "concurrently \"npm run start:dev\" \"npm run react-client\" --names \"server,react\" --prefix-colors \"blue,green\"", "check-app1": "node dist/check-app.js", "check-app": "exit 0", "release": "semantic-release"}, "dependencies": {"@apollo/subgraph": "2.2.3", "@hocuspocus/extension-redis": "^2.15.2", "@hocuspocus/server": "^2.15.2", "@hocuspocus/transformer": "^2.15.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.4.6", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.6", "@nestjs/cqrs": "^10.2.8", "@nestjs/event-emitter": "^2.1.1", "@nestjs/graphql": "^13.1.0", "@nestjs/platform-express": "^10.4.6", "@nestjs/platform-socket.io": "^11.1.2", "@nestjs/schedule": "^5.0.1", "@nestjs/terminus": "^10.2.3", "@nestjs/websockets": "^11.1.2", "@prisma/client": "^6.1.0", "@skillspace/access": "1.0.5", "@skillspace/common": "^1.2.0", "@skillspace/cqrs": "1.0.0", "@skillspace/graphql": "1.3.1", "@skillspace/grpc": "^1.7.0", "@skillspace/logger": "4.2.0", "@skillspace/tracing": "1.0.1", "@skillspace/utils": "0.1.1", "@socket.io/redis-adapter": "^8.3.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-dropcursor": "^2.14.0", "@tiptap/extension-gapcursor": "^2.14.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@tiptap/html": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "bullmq": "^5.53.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.4.5", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "express": "^5.1.0", "graphql": "^16.11.0", "graphql-type-json": "^0.3.2", "mime-types": "^3.0.1", "nanoid": "^5.1.5", "only-allow": "^1.2.1", "pg": "^8.16.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.2", "uuid": "^11.1.0", "ws": "^8.18.2", "yjs": "^13.6.27"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/compat": "^1.2.2", "@eslint/js": "^9.23.0", "@golevelup/ts-jest": "^0.5.6", "@nestjs/cli": "^10.4.7", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.4", "@skillspace/eslint-service": "^1.2.2", "@skillspace/hermes": "^1.0.2", "@testcontainers/postgresql": "^11.0.0", "@testcontainers/redis": "^11.0.0", "@types/eslint": "^9.6.1", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/mime-types": "^2.1.4", "@types/node": "^22.9.0", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "concurrently": "^9.1.2", "conventional-changelog-cli": "2.2.2", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.2.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.12.0", "graphql-tag": "^2.12.6", "husky": "^8.0.0", "jest": "^29.7.0", "prettier": "^3.3.3", "semantic-release": "24.2.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "tsconfig-paths": "4.2.0", "typescript": "^5.6.3", "typescript-eslint": "^8.30.1"}, "pnpm": {"overrides": {}}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}