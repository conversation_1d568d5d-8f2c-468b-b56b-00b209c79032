# 📝 Сервис редактирования документов

Сервис для совместного редактирования документов в реальном времени с использованием Hocuspocus и Y.js.

## 🚀 Быстрый старт

### Запуск основного приложения
```bash
# Установка зависимостей
pnpm install

# Запуск в режиме разработки
pnpm start:dev
```

### Тестирование клиентов

#### React-клиент (рекомендуется)
```bash
# Запуск сервера и React-клиента одновременно
pnpm dev:full

# Только React-клиент
pnpm react-client
# Откройте: http://localhost:5173
```

#### HTML-клиенты
```bash
# Отладочный клиент
pnpm client:debug
# Откройте: http://localhost:8081/debug-client.html

# Полнофункциональный тестовый клиент
pnpm client:test
# Откройте: http://localhost:8082/simple-client.html

# Оригинальный клиент
pnpm client
# Откройте: http://localhost:8080
```

## 🔧 Основные команды

### Разработка
- `pnpm start:dev` - запуск в режиме разработки
- `pnpm build` - сборка проекта
- `pnpm lint` - проверка кода
- `pnpm format` - форматирование кода

### Тестирование
- `pnpm test` - запуск всех тестов
- `pnpm test:integration` - интеграционные тесты
- `pnpm test:ws` - тесты WebSocket

### Клиенты
- `pnpm client:debug` - отладочный клиент (порт 8081)
- `pnpm client:test` - тестовый клиент (порт 8082)
- `pnpm client` - legacy клиент (порт 8080)

### База данных
- `pnpm db:generate` - генерация миграций
- `pnpm db:migrate` - применение миграций
- `pnpm db:studio` - Drizzle Studio

## 🏗️ Архитектура

### Технологии
- **Backend**: NestJS, GraphQL, WebSocket
- **Database**: PostgreSQL + Drizzle ORM
- **Collaboration**: Hocuspocus + Y.js
- **Cache**: Redis (опционально)
- **Testing**: Jest + Testcontainers

### Модули
- **EditorModule**: управление страницами через GraphQL
- **CollaborationModule**: совместное редактирование через WebSocket
- **QueueModule**: фоновые задачи через BullMQ

## 📚 Документация

- [Тестовые клиенты](./client/README.md) - подробная документация по клиентам
- [Тесты](./tests/README.md) - информация о тестировании

## 🔗 Полезные ссылки

- **Приложение**: http://localhost:3033
- **GraphQL Playground**: http://localhost:3033/graphql
- **React Client**: http://localhost:5173
- **Debug Client**: http://localhost:8081/debug-client.html
- **Test Client**: http://localhost:8082/simple-client.html

