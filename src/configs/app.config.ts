export const NODE_ENV = {
    DEV: 'development',
    PROD: 'production',
    TEST: 'test',
} as const;

export interface IAppConfig {
    port: number;
    nodeEnv: string;
    jwtSecret: string;
    dbUrl: string;
    redisUrl: string;
    editorGrpcAddress: string;
    isCollabDisableRedis: () => boolean;
}

const nodeEnv = process.env.NODE_ENV;

export const isTest = nodeEnv === NODE_ENV.TEST;
export const isProd = nodeEnv === NODE_ENV.PROD;

export const appConfig: IAppConfig = {
    port: Number(process.env.APP_PORT),
    nodeEnv,
    jwtSecret: process.env.JWT_SECRET,
    dbUrl: process.env.DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    editorGrpcAddress: process.env.EDITOR_GRPC_ADDRESS,
    isCollabDisableRedis: () => process.env.COLLAB_DISABLE_REDIS === 'true',
};
