import { INestApplication, ValidationPipe } from '@nestjs/common';
import { graphqlUploadExpress } from '@skillspace/graphql';

export function setupGlobalMiddlewares(app: INestApplication) {
    // Enable CORS for all origins (development only)
    app.enableCors({
        origin: true,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    });

    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    app.use(graphqlUploadExpress({ maxFileSize: 5 * 1024 * 1024, maxFiles: 5 }));
}
