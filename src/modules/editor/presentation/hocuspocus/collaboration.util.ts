import { generateText, getSchema, J<PERSON>NContent } from '@tiptap/core';
import { Color } from '@tiptap/extension-color';
import { Dropcursor } from '@tiptap/extension-dropcursor';
import { Gapcursor } from '@tiptap/extension-gapcursor';
import { Highlight } from '@tiptap/extension-highlight';
import { Image } from '@tiptap/extension-image';
import { Link } from '@tiptap/extension-link';
import { Placeholder } from '@tiptap/extension-placeholder';
import SubScript from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { Table } from '@tiptap/extension-table';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableRow } from '@tiptap/extension-table-row';
import { TaskItem } from '@tiptap/extension-task-item';
import { TaskList } from '@tiptap/extension-task-list';
import { TextAlign } from '@tiptap/extension-text-align';
import { TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import { Youtube } from '@tiptap/extension-youtube';
import { generateHTML, generateJSON } from '@tiptap/html';
import { Node } from '@tiptap/pm/model';
import { StarterKit } from '@tiptap/starter-kit';

export const tiptapExtensions = [
    StarterKit.configure({
        dropcursor: false,
        gapcursor: false,
    }),
    Typography,
    Dropcursor,
    Gapcursor,
    Underline,
    Link,
    Image,
    TextAlign.configure({ types: ['heading', 'paragraph'] }),
    Table,
    TableCell,
    TableHeader,
    TableRow,
    TaskList,
    TaskItem,
    TextStyle,
    Color,
    Highlight.configure({
        multicolor: true,
    }),
    Placeholder.configure({
        placeholder: ({ node }) => {
            if (node.type.name === 'heading') {
                return 'Заголовок страницы';
            }
            return 'Содержимое страницы';
        },
    }),
    Superscript,
    SubScript,
    Youtube,
];

export function jsonToHtml(tiptapJson: JSONContent) {
    return generateHTML(tiptapJson, tiptapExtensions);
}

export function htmlToJson(html: string) {
    return generateJSON(html, tiptapExtensions);
}

export function jsonToText(tiptapJson: JSONContent): string {
    return generateText(tiptapJson, tiptapExtensions);
}

export function jsonToNode(tiptapJson: JSONContent) {
    return Node.fromJSON(getSchema(tiptapExtensions), tiptapJson);
}

export function getPageId(documentName: string) {
    if (!documentName) {
        return undefined;
    }
    // documentName теперь это просто pageId
    return documentName;
}
