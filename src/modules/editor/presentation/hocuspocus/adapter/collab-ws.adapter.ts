import { IncomingMessage, Server } from 'node:http';
import { Socket } from 'node:net';
import { Logger } from '@nestjs/common';
import { WebSocket, WebSocketServer } from 'ws';

interface SecureWebSocket extends WebSocket {
    isSecure: boolean;
}

export class CollabWsAdapter {
    private readonly wss: WebSocketServer;
    private readonly logger = new Logger(CollabWsAdapter.name);

    constructor() {
        this.wss = new WebSocketServer({ noServer: true });
    }

    handleUpgrade(path: string, httpServer: Server) {
        httpServer.on('upgrade', (request: IncomingMessage, socket: Socket, head: Buffer) => {
            try {
                // Определяем протокол из заголовков или используем ws по умолчанию
                const isSecure = request.headers['x-forwarded-proto'] === 'https';
                const protocol = isSecure ? 'wss://' : 'ws://';
                const baseUrl = protocol + request.headers.host + '/';
                const pathname = new URL(request.url, baseUrl).pathname;

                this.logger.log(`WebSocket upgrade request: ${request.url}`);
                this.logger.log(`Protocol: ${protocol}, Host: ${request.headers.host}`);
                this.logger.log(`Pathname: ${pathname}, Expected path: ${path}`);
                this.logger.log(`Connection type: ${isSecure ? 'Secure (WSS)' : 'Insecure (WS)'}`);

                if (pathname === path) {
                    this.wss.handleUpgrade(request, socket, head, (ws: WebSocket) => {
                        // Добавляем информацию о типе соединения
                        (ws as SecureWebSocket).isSecure = isSecure;
                        this.wss.emit('connection', ws, request);
                    });
                } else {
                    this.logger.warn(`WebSocket path mismatch: ${pathname} !== ${path}`);
                    socket.destroy();
                }
            } catch (err) {
                this.logger.error('WebSocket upgrade error:', err);
                socket.end('HTTP/1.1 400\r\n' + (err as Error).message);
            }
        });

        return this.wss;
    }

    public destroy() {
        try {
            this.wss.clients.forEach((client) => {
                client.terminate();
            });
            this.wss.close();
        } catch (err) {
            this.logger.error('Error during WebSocket server shutdown:', err);
        }
    }
}
