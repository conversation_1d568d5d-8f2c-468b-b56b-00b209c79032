import { Logger } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';

import { PageService } from '../../application/services/page.service';
import { PageType } from './page.output';

@Resolver(() => PageType)
export class PageResolver {
    private readonly logger = new Logger(PageResolver.name);

    constructor(private readonly pageService: PageService) {}

    @Query(() => PageType, { name: 'page' })
    async getPage(
        @Args('id', { type: () => ID })
        id: string,
    ): Promise<PageType> {
        this.logger.log(`Getting page by ID: ${id}`);
        return (await this.pageService.findById(id)) as PageType;
    }

    @Mutation(() => Boolean, { name: 'deletePage' })
    async deletePage(
        @Args('id', { type: () => ID })
        id: string,
    ): Promise<boolean> {
        this.logger.log(`Deleting page: ${id}`);
        await this.pageService.forceDelete(id);
        return true;
    }
}
