import { BullModule } from '@nestjs/bullmq';
import { Global, Module } from '@nestjs/common';

import { appConfig } from '../../../../configs/app.config';
import { QueueName } from './constants';

@Global()
@Module({
    imports: [
        BullModule.forRootAsync({
            useFactory: () => {
                return {
                    connection: {
                        url: appConfig.redisUrl,
                    },
                    defaultJobOptions: {
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 20 * 1000,
                        },
                        removeOnComplete: {
                            count: 200,
                        },
                        removeOnFail: {
                            count: 100,
                        },
                    },
                };
            },
        }),
        BullModule.registerQueue({
            name: QueueName.GENERAL_QUEUE,
        }),
    ],
    exports: [BullModule],
})
export class QueueModule {}
