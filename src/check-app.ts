import 'reflect-metadata';

import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';

async function bootstrap() {
    try {
        // Создаем контекст приложения
        const app = await NestFactory.createApplicationContext(AppModule);

        console.log('Все зависимости успешно зарезолвены');

        // Закрываем приложение
        await app.close();
    } catch (error) {
        console.error('Ошибка во время выполнения проверки приложения:', error);
        process.exit(1); // Завершаем с ошибкой
    }
}

void bootstrap();
