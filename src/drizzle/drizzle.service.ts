import { Injectable, Logger, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool, PoolClient } from 'pg';

import { appConfig } from '../configs/app.config';
import { DrizzleDB } from './drizzle.types';

@Injectable()
export class DrizzleService implements OnModuleInit, OnModuleDestroy {
    private pool: Pool;
    public readonly db: DrizzleDB;
    private readonly logger = new Logger(DrizzleService.name);

    constructor() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
        this.pool = new Pool({ connectionString: appConfig.dbUrl });
        // eslint-disable-next-line
        this.db = drizzle(this.pool);
    }

    async onModuleInit() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        const client: PoolClient = await this.pool.connect();
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        client.release();
        this.logger.log('✅ DrizzleService connected to Postgres');
    }

    async onModuleDestroy(): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        await this.pool.end();
    }
}
