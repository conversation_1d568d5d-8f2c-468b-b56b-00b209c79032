import { sql } from 'drizzle-orm';
import { boolean, jsonb, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { customType } from 'drizzle-orm/pg-core';

// Кастомный тип для бинарных данных (bytea)
const bytea = customType<{ data: Buffer; notNull: false; default: false }>({
    dataType() {
        return 'bytea';
    },
    toDriver(value: Buffer): Buffer {
        return value;
    },
    fromDriver(value: Buffer): Buffer {
        return value;
    },
});

export const pages = pgTable('pages', {
    id: uuid('id').primaryKey(),

    // Обязательные поля для идентификации документа
    serviceId: varchar('service_id').notNull(), // webinars, courses
    entityId: varchar('entity_id').notNull(), // doc, page e.t.c.
    schoolId: varchar('school_id').notNull(),

    // Y.js документ в бинарном формате
    ydoc: bytea('ydoc'),

    // Дополнительные поля для документа
    content: jsonb('content'),
    textContent: text('text_content'),

    isLocked: boolean('is_locked').default(false).notNull(),

    createdAt: timestamp('created_at')
        .default(sql`now()`)
        .notNull(),
    updatedAt: timestamp('updated_at')
        .default(sql`now()`)
        .notNull(),
    deletedAt: timestamp('deleted_at'),
});

export type Page = typeof pages.$inferSelect;
export type InsertablePage = typeof pages.$inferInsert;
export type UpdatablePage = Partial<Omit<InsertablePage, 'id'>>;
