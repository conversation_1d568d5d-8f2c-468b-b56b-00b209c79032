import { DrizzleDB, DrizzleTransaction } from './drizzle.types';

/*
 * Executes a transaction or a callback using the provided database instance.
 * If an existing transaction is provided, it directly executes the callback with it.
 * Otherwise, it starts a new transaction using the provided database instance and executes the callback within that transaction.
 */
export async function executeTx<T>(
    db: Dr<PERSON>zleDB,
    callback: (trx: DrizzleTransaction) => Promise<T>,
    existingTrx?: DrizzleTransaction,
): Promise<T> {
    if (existingTrx) {
        return await callback(existingTrx); // Execute callback with existing transaction
    } else {
        return await db.transaction((trx) => callback(trx)); // Start new transaction and execute callback
    }
}

/*
 * This function returns either an existing transaction if provided,
 * or the normal database instance.
 */
export function dbOrTx(db: DrizzleDB, existingTrx?: DrizzleTransaction): DrizzleDB | DrizzleTransaction {
    if (existingTrx) {
        return existingTrx; // Use existing transaction
    } else {
        return db; // Use normal database instance
    }
}
