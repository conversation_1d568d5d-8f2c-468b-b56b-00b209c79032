{"name": "tiptap-collab-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "devDependencies": {"@eslint/js": "^9.25.0", "@hocuspocus/provider": "^3.1.1", "@tiptap/core": "^2.12.0", "@tiptap/extension-collaboration": "^2.12.0", "@tiptap/extension-collaboration-cursor": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8", "vite": "^6.3.5", "y-prosemirror": "^1.3.5", "yjs": "^13.6.27"}, "dependencies": {"@tiptap/extension-color": "^2.14.0", "@tiptap/extension-dropcursor": "^2.14.0", "@tiptap/extension-gapcursor": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/extension-youtube": "^2.14.0", "uuid": "^11.1.0"}}