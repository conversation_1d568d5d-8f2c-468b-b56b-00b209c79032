/* TipTap Editor Styles */
.ProseMirror {
    outline: none;
    padding: 16px;
    min-height: 400px;
    font-family:
        system-ui,
        -apple-system,
        sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #374151;
}

.ProseMirror p {
    margin: 0 0 16px 0;
}

.ProseMirror p:last-child {
    margin-bottom: 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
    margin: 24px 0 16px 0;
    font-weight: 600;
    line-height: 1.3;
}

.ProseMirror h1 {
    font-size: 2em;
}

.ProseMirror h2 {
    font-size: 1.5em;
}

.ProseMirror h3 {
    font-size: 1.25em;
}

.ProseMirror ul,
.ProseMirror ol {
    margin: 16px 0;
    padding-left: 24px;
}

.ProseMirror li {
    margin: 4px 0;
}

/* Table styles */
.ProseMirror table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 16px 0;
    overflow: hidden;
}

.ProseMirror td,
.ProseMirror th {
    min-width: 1em;
    border: 2px solid #ced4da;
    padding: 3px 5px;
    vertical-align: top;
    box-sizing: border-box;
    position: relative;
}

.ProseMirror th {
    font-weight: bold;
    text-align: left;
    background-color: #f1f3f4;
}

.ProseMirror .selectedCell:after {
    z-index: 2;
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(200, 200, 255, 0.4);
    pointer-events: none;
}

.ProseMirror .column-resize-handle {
    position: absolute;
    right: -2px;
    top: 0;
    bottom: -2px;
    width: 4px;
    background-color: #adf;
    pointer-events: none;
}

.ProseMirror.resize-cursor {
    cursor: ew-resize;
    cursor: col-resize;
}

/* Task list styles */
.ProseMirror ul[data-type='taskList'] {
    list-style: none;
    padding: 0;
}

.ProseMirror ul[data-type='taskList'] p {
    margin: 0;
}

.ProseMirror ul[data-type='taskList'] li {
    display: flex;
    align-items: flex-start;
}

.ProseMirror ul[data-type='taskList'] li > label {
    flex: 0 0 auto;
    margin-right: 0.5rem;
    user-select: none;
}

.ProseMirror ul[data-type='taskList'] li > div {
    flex: 1 1 auto;
}

.ProseMirror ul[data-type='taskList'] input[type='checkbox'] {
    cursor: pointer;
}

.ProseMirror ul[data-type='taskList'] li[data-checked='true'] > div > p {
    color: #a0a0a0;
    text-decoration: line-through;
    text-decoration-thickness: 2px;
}

/* Highlight styles */
.ProseMirror mark {
    background-color: #faf594;
    border-radius: 0.25rem;
    box-decoration-break: clone;
    padding: 0.125rem 0;
}

.ProseMirror mark[data-color='#958DF1'] {
    background-color: #958df1;
}

.ProseMirror mark[data-color='#F98181'] {
    background-color: #f98181;
}

.ProseMirror mark[data-color='#FBBC88'] {
    background-color: #fbbc88;
}

.ProseMirror mark[data-color='#FAF594'] {
    background-color: #faf594;
}

.ProseMirror mark[data-color='#70CFF8'] {
    background-color: #70cff8;
}

.ProseMirror mark[data-color='#94FADB'] {
    background-color: #94fadb;
}

.ProseMirror mark[data-color='#B9F18D'] {
    background-color: #b9f18d;
}

/* Text alignment */
.ProseMirror .has-focus {
    border-radius: 3px;
    box-shadow: 0 0 0 3px #68cef8;
}

.ProseMirror p.has-focus {
    outline: none;
}

.ProseMirror [data-text-align='left'] {
    text-align: left;
}

.ProseMirror [data-text-align='center'] {
    text-align: center;
}

.ProseMirror [data-text-align='right'] {
    text-align: right;
}

.ProseMirror [data-text-align='justify'] {
    text-align: justify;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

/* YouTube embed */
.ProseMirror .youtube-embed {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
    margin: 16px 0;
}

.ProseMirror .youtube-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Image styles */
.ProseMirror img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

.ProseMirror img.ProseMirror-selectednode {
    outline: 3px solid #68cef8;
}

/* Superscript and subscript */
.ProseMirror sup {
    vertical-align: super;
    font-size: smaller;
}

.ProseMirror sub {
    vertical-align: sub;
    font-size: smaller;
}

/* Link styles */
.ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
    cursor: pointer;
}

.ProseMirror a:hover {
    color: #1d4ed8;
}

/* Dropcursor */
.ProseMirror .ProseMirror-dropcursor {
    position: relative;
    pointer-events: none;
}

.ProseMirror .ProseMirror-dropcursor:after {
    content: '';
    display: block;
    position: absolute;
    left: -2px;
    right: -2px;
    top: -2px;
    bottom: -2px;
    border: 2px solid #3b82f6;
    border-radius: 4px;
}

/* Gapcursor */
.ProseMirror .ProseMirror-gapcursor {
    display: none;
    pointer-events: none;
    position: absolute;
}

.ProseMirror .ProseMirror-gapcursor:after {
    content: '';
    display: block;
    position: absolute;
    top: -2px;
    width: 20px;
    border-top: 1px solid black;
    animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
    to {
        visibility: hidden;
    }
}

.ProseMirror blockquote {
    border-left: 4px solid #e5e7eb;
    margin: 16px 0;
    padding-left: 16px;
    color: #6b7280;
    font-style: italic;
}

.ProseMirror code {
    background-color: #f3f4f6;
    border-radius: 4px;
    padding: 2px 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

.ProseMirror pre {
    background-color: #f3f4f6;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;
}

.ProseMirror pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.ProseMirror strong {
    font-weight: 600;
}

.ProseMirror em {
    font-style: italic;
}

.ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 24px 0;
}

/* Collaboration cursor styles */
.collaboration-cursor__caret {
    border-left: 1px solid #0d0d0d;
    border-right: 1px solid #0d0d0d;
    margin-left: -1px;
    margin-right: -1px;
    pointer-events: none;
    position: relative;
    word-break: normal;
}

.collaboration-cursor__label {
    border-radius: 3px 3px 3px 0;
    color: #0d0d0d;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    left: -1px;
    line-height: normal;
    padding: 0.1rem 0.3rem;
    position: absolute;
    top: -1.4em;
    user-select: none;
    white-space: nowrap;
}

/* Focus styles */
.ProseMirror:focus {
    outline: none;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
    color: #9ca3af;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

/* Selection styles */
.ProseMirror ::selection {
    background-color: #dbeafe;
}

.ProseMirror ::-moz-selection {
    background-color: #dbeafe;
}
