import React from 'react';

const Toolbar = ({ editor }) => {
    if (!editor) {
        return null;
    }

    const buttonStyle = {
        padding: '8px 12px',
        margin: '2px',
        border: '1px solid #d1d5db',
        borderRadius: '4px',
        backgroundColor: '#ffffff',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        transition: 'all 0.2s',
    };

    const activeButtonStyle = {
        ...buttonStyle,
        backgroundColor: '#3b82f6',
        color: 'white',
        borderColor: '#3b82f6',
    };

    const toolbarStyle = {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '4px',
        padding: '12px',
        borderBottom: '1px solid #e5e7eb',
        backgroundColor: '#f9fafb',
        borderRadius: '8px 8px 0 0',
    };

    const groupStyle = {
        display: 'flex',
        gap: '2px',
        paddingRight: '8px',
        marginRight: '8px',
        borderRight: '1px solid #d1d5db',
    };

    return (
        <div style={toolbarStyle}>
            {/* Заголовки */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                    style={editor.isActive('heading', { level: 1 }) ? activeButtonStyle : buttonStyle}
                >
                    H1
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                    style={editor.isActive('heading', { level: 2 }) ? activeButtonStyle : buttonStyle}
                >
                    H2
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                    style={editor.isActive('heading', { level: 3 }) ? activeButtonStyle : buttonStyle}
                >
                    H3
                </button>
            </div>

            {/* Форматирование */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    style={editor.isActive('bold') ? activeButtonStyle : buttonStyle}
                >
                    <strong>B</strong>
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    style={editor.isActive('italic') ? activeButtonStyle : buttonStyle}
                >
                    <em>I</em>
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                    style={editor.isActive('underline') ? activeButtonStyle : buttonStyle}
                >
                    <u>U</u>
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    style={editor.isActive('strike') ? activeButtonStyle : buttonStyle}
                >
                    <s>S</s>
                </button>
            </div>

            {/* Надстрочный/подстрочный */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleSuperscript().run()}
                    style={editor.isActive('superscript') ? activeButtonStyle : buttonStyle}
                >
                    X²
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleSubscript().run()}
                    style={editor.isActive('subscript') ? activeButtonStyle : buttonStyle}
                >
                    X₂
                </button>
            </div>

            {/* Цвет текста */}
            <div style={groupStyle}>
                <button onClick={() => editor.chain().focus().setColor('#ff0000').run()} style={buttonStyle}>
                    🔴 Текст
                </button>
                <button onClick={() => editor.chain().focus().setColor('#00ff00').run()} style={buttonStyle}>
                    🟢 Текст
                </button>
                <button onClick={() => editor.chain().focus().setColor('#0000ff').run()} style={buttonStyle}>
                    🔵 Текст
                </button>
                <button onClick={() => editor.chain().focus().unsetColor().run()} style={buttonStyle}>
                    ⚫ Сброс
                </button>
            </div>

            {/* Выделение */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleHighlight().run()}
                    style={editor.isActive('highlight') ? activeButtonStyle : buttonStyle}
                >
                    🖍️ Выделить
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleHighlight({ color: '#ff0000' }).run()}
                    style={buttonStyle}
                >
                    🔴
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleHighlight({ color: '#00ff00' }).run()}
                    style={buttonStyle}
                >
                    🟢
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleHighlight({ color: '#0000ff' }).run()}
                    style={buttonStyle}
                >
                    🔵
                </button>
            </div>

            {/* Выравнивание */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().setTextAlign('left').run()}
                    style={editor.isActive({ textAlign: 'left' }) ? activeButtonStyle : buttonStyle}
                >
                    ⬅️
                </button>
                <button
                    onClick={() => editor.chain().focus().setTextAlign('center').run()}
                    style={editor.isActive({ textAlign: 'center' }) ? activeButtonStyle : buttonStyle}
                >
                    ↔️
                </button>
                <button
                    onClick={() => editor.chain().focus().setTextAlign('right').run()}
                    style={editor.isActive({ textAlign: 'right' }) ? activeButtonStyle : buttonStyle}
                >
                    ➡️
                </button>
                <button
                    onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                    style={editor.isActive({ textAlign: 'justify' }) ? activeButtonStyle : buttonStyle}
                >
                    ⬌
                </button>
            </div>

            {/* Списки */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    style={editor.isActive('bulletList') ? activeButtonStyle : buttonStyle}
                >
                    • Список
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    style={editor.isActive('orderedList') ? activeButtonStyle : buttonStyle}
                >
                    1. Список
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleTaskList().run()}
                    style={editor.isActive('taskList') ? activeButtonStyle : buttonStyle}
                >
                    ☑️ Задачи
                </button>
            </div>

            {/* Таблица */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
                    style={buttonStyle}
                >
                    📊 Таблица
                </button>
                {editor.isActive('table') && (
                    <>
                        <button onClick={() => editor.chain().focus().addColumnBefore().run()} style={buttonStyle}>
                            ➕ Колонка
                        </button>
                        <button onClick={() => editor.chain().focus().addRowBefore().run()} style={buttonStyle}>
                            ➕ Строка
                        </button>
                        <button onClick={() => editor.chain().focus().deleteTable().run()} style={buttonStyle}>
                            🗑️ Удалить
                        </button>
                    </>
                )}
            </div>

            {/* Другое */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().toggleBlockquote().run()}
                    style={editor.isActive('blockquote') ? activeButtonStyle : buttonStyle}
                >
                    💬 Цитата
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleCode().run()}
                    style={editor.isActive('code') ? activeButtonStyle : buttonStyle}
                >
                    &lt;/&gt; Код
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                    style={editor.isActive('codeBlock') ? activeButtonStyle : buttonStyle}
                >
                    📝 Блок кода
                </button>
            </div>

            {/* Ссылки и медиа */}
            <div style={groupStyle}>
                <button
                    onClick={() => {
                        const url = window.prompt('URL ссылки:');
                        if (url) {
                            editor.chain().focus().setLink({ href: url }).run();
                        }
                    }}
                    style={editor.isActive('link') ? activeButtonStyle : buttonStyle}
                >
                    🔗 Ссылка
                </button>
                <button
                    onClick={() => {
                        const url = window.prompt('URL изображения:');
                        if (url) {
                            editor.chain().focus().setImage({ src: url }).run();
                        }
                    }}
                    style={buttonStyle}
                >
                    🖼️ Картинка
                </button>
                <button
                    onClick={() => {
                        const url = window.prompt('URL YouTube видео:');
                        if (url) {
                            editor.commands.setYoutubeVideo({ src: url });
                        }
                    }}
                    style={buttonStyle}
                >
                    📺 YouTube
                </button>
            </div>

            {/* Отмена/повтор */}
            <div style={groupStyle}>
                <button
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().undo()}
                    style={buttonStyle}
                >
                    ↶ Отмена
                </button>
                <button
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().redo()}
                    style={buttonStyle}
                >
                    ↷ Повтор
                </button>
            </div>
        </div>
    );
};

export default Toolbar;
