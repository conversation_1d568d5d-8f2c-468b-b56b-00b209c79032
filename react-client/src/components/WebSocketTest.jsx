import React, { useState } from 'react';

const WebSocketTest = () => {
    const [ws, setWs] = useState(null);
    const [status, setStatus] = useState('disconnected');
    const [messages, setMessages] = useState([]);
    const [error, setError] = useState(null);

    const addMessage = (message) => {
        setMessages((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    const connect = () => {
        setError(null);
        setStatus('connecting');
        addMessage('Attempting to connect to ws://localhost:3033/collab');

        const websocket = new WebSocket('ws://localhost:3033/collab');

        websocket.onopen = () => {
            setStatus('connected');
            addMessage('✅ WebSocket connection opened');
        };

        websocket.onmessage = (event) => {
            addMessage(`📨 Received: ${event.data}`);
        };

        websocket.onerror = (event) => {
            setError('WebSocket error occurred');
            addMessage(`❌ WebSocket error: ${event}`);
        };

        websocket.onclose = (event) => {
            setStatus('disconnected');
            addMessage(`🔌 WebSocket closed. Code: ${event.code}, Reason: ${event.reason || 'No reason'}`);

            if (event.code !== 1000) {
                setError(`Connection closed with code ${event.code}: ${event.reason || 'Unknown error'}`);
            }
        };

        setWs(websocket);
    };

    const disconnect = () => {
        if (ws) {
            ws.close();
            setWs(null);
        }
        setStatus('disconnected');
        setError(null);
    };

    const sendMessage = () => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            const message = JSON.stringify({
                type: 'test',
                data: 'Hello from client',
            });
            ws.send(message);
            addMessage(`📤 Sent: ${message}`);
        }
    };

    const clearMessages = () => {
        setMessages([]);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>WebSocket Connection Test</h1>

            <div style={{ marginBottom: '20px' }}>
                <p>
                    Status:{' '}
                    <strong
                        style={{
                            color: status === 'connected' ? 'green' : status === 'connecting' ? 'orange' : 'red',
                        }}
                    >
                        {status}
                    </strong>
                </p>

                {error && (
                    <p style={{ color: 'red', backgroundColor: '#ffe6e6', padding: '10px', borderRadius: '4px' }}>
                        Error: {error}
                    </p>
                )}

                <div style={{ marginBottom: '10px' }}>
                    <button
                        onClick={status === 'connected' ? disconnect : connect}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: status === 'connected' ? '#dc3545' : '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            marginRight: '10px',
                        }}
                    >
                        {status === 'connected' ? 'Disconnect' : 'Connect'}
                    </button>

                    <button
                        onClick={sendMessage}
                        disabled={status !== 'connected'}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: status === 'connected' ? '#28a745' : '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: status === 'connected' ? 'pointer' : 'not-allowed',
                            marginRight: '10px',
                        }}
                    >
                        Send Test Message
                    </button>

                    <button
                        onClick={clearMessages}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }}
                    >
                        Clear Messages
                    </button>
                </div>
            </div>

            <div
                style={{
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    padding: '10px',
                    backgroundColor: '#f8f9fa',
                    height: '300px',
                    overflowY: 'auto',
                }}
            >
                <h3>Messages:</h3>
                {messages.length === 0 ? (
                    <p style={{ color: '#6c757d' }}>No messages yet...</p>
                ) : (
                    messages.map((message, index) => (
                        <div
                            key={index}
                            style={{
                                marginBottom: '5px',
                                fontFamily: 'monospace',
                                fontSize: '14px',
                            }}
                        >
                            {message}
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default WebSocketTest;
