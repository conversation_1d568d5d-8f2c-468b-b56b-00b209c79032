# React TipTap Collaborative Editor

Современный React-клиент для тестирования совместного редактирования с использованием TipTap и Hocuspocus.

## 🚀 Возможности

- ⚛️ **React 19** с современными хуками
- 📝 **TipTap Editor** с богатым функционалом
- 👥 **Совместное редактирование** в реальном времени
- 🎨 **Курсоры пользователей** с именами и цветами
- 🔌 **WebSocket соединение** через Hocuspocus
- 🎯 **Y.js** для синхронизации документов
- ⚡ **Vite** для быстрой разработки

## 📦 Установка

Все зависимости установлены как `devDependencies`, так как клиент используется только для локальной разработки.

```bash
# Установка зависимостей (выполняется автоматически)
cd react-client
npm install
```

## 🎮 Запуск

### Быстрый старт (рекомендуется)
```bash
# Запуск сервера и React-клиента одновременно
pnpm dev:full
```

### Отдельный запуск
```bash
# Только React-клиент
pnpm react-client

# Или из директории react-client
cd react-client
npm run dev
```

## 🔧 Доступные компоненты

### 1. CollaborativeEditor (по умолчанию)
Полнофункциональный редактор с:
- Настройкой URL сервера
- Выбором имени документа
- Настройкой пользователя (имя и цвет)
- Статусом подключения
- Подробными логами

### 2. SimpleEditor
Упрощенная версия для быстрого тестирования

### 3. WebSocketTest
Базовый тест WebSocket соединения

Переключение между компонентами в `src/App.jsx`:
```jsx
// Полный collaborative editor (по умолчанию)
return <CollaborativeEditor />;

// Простой редактор
// return <SimpleEditor />;

// Тест WebSocket
// return <WebSocketTest />;
```

## 🌐 Настройки подключения

По умолчанию клиент подключается к:
- **Сервер**: `ws://localhost:3033/collab`
- **Документ**: `example-document`
- **Порт клиента**: `5173`

## 🧪 Тестирование совместного редактирования

1. Запустите сервер: `pnpm start:dev`
2. Запустите React-клиент: `pnpm react-client`
3. Откройте несколько вкладок браузера на `http://localhost:5173`
4. Подключитесь к одному документу с разных вкладок
5. Начните печатать - изменения будут синхронизироваться в реальном времени

## 🔗 Полезные ссылки

- **React Client**: http://localhost:5173
- **Основной сервер**: http://localhost:3033
- **GraphQL Playground**: http://localhost:3033/graphql

## 🛠️ Технологии

- **React 19** - UI библиотека
- **TipTap** - Rich text editor
- **Y.js** - CRDT для совместного редактирования
- **Hocuspocus Provider** - WebSocket провайдер для Y.js
- **Vite** - Сборщик и dev сервер
- **Tailwind CSS** - Стилизация
